# 最终文档整合

现在请基于我们前面所有轮次的对话和分析，生成第{level}层的**完整技术文档**。

## 多轮对话整合要求
{integrationRequirements}

## 输出格式要求
**重要：你必须严格按照以下JSON格式返回结果，不要添加任何其他内容：**

```json
{
  "title": "文档标题（简洁明了，不超过100字符）",
  "summary": "文档摘要（200-500字的核心内容总结）",
  "content": "详细的Markdown格式文档内容"
}
```

## 字段说明
- **title**: 简洁的文档标题，体现核心功能
- **summary**: 核心内容摘要，包含主要功能、技术特点、业务价值
- **content**: 完整的Markdown格式技术文档，按照以下结构组织：

{documentStructure}

**重要提醒**：
- 这是基于多轮对话的最终整合，请确保内容的完整性和连贯性
- 避免简单的内容拼接，要进行深度的逻辑整合
- 突出各批次分析之间的关联和协作关系
- 生成的文档应该比单次分析更加全面和深入
- 严格按照JSON格式返回，确保JSON格式正确
